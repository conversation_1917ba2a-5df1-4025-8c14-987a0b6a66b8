#!/usr/bin/env elixir

# Example demonstrating custom telemetry identifier configuration
# Run with: mix run examples/telemetry_custom_identifier.exs

defmodule TelemetryCustomIdentifierExample do
  @moduledoc """
  Example showing how to configure custom telemetry identifiers for Operations.
  
  This example demonstrates:
  1. Default telemetry events with :drops identifier
  2. Custom telemetry events with :my_app identifier
  3. How to set up telemetry handlers for both
  """

  defmodule TelemetryHandler do
    require Logger

    def handle_event([identifier, :operation, :start], _measurements, metadata, _config) do
      Logger.info("🚀 [#{identifier}] Starting operation #{inspect(metadata.operation)} with step #{metadata.step}")
    end

    def handle_event([identifier, :operation, :stop], measurements, metadata, _config) do
      duration_ms = System.convert_time_unit(measurements.duration, :native, :millisecond)
      Logger.info("✅ [#{identifier}] Completed operation #{inspect(metadata.operation)} in #{duration_ms}ms")
    end

    def handle_event([identifier, :operation, :step, :start], _measurements, metadata, _config) do
      Logger.info("🔧 [#{identifier}] Starting step #{metadata.step} in #{inspect(metadata.operation)}")
    end

    def handle_event([identifier, :operation, :step, :stop], measurements, metadata, _config) do
      duration_ms = System.convert_time_unit(measurements.duration, :native, :millisecond)
      Logger.info("🔧 [#{identifier}] Completed step #{metadata.step} in #{duration_ms}ms")
    end

    def handle_event([identifier, :operation, :exception], _measurements, metadata, _config) do
      Logger.error("❌ [#{identifier}] Failed in #{inspect(metadata.operation)}: #{inspect(metadata.reason)}")
    end

    def handle_event([identifier, :operation, :step, :exception], _measurements, metadata, _config) do
      Logger.error("❌ [#{identifier}] Failed step #{metadata.step} in #{inspect(metadata.operation)}: #{inspect(metadata.reason)}")
    end
  end

  # Operation with default telemetry (uses :drops identifier)
  defmodule DefaultTelemetryOperation do
    use Drops.Operations.Command, telemetry: true

    steps do
      @impl true
      def execute(%{params: params}) do
        Process.sleep(10) # Simulate some work
        {:ok, Map.put(params, :processed_by, :default_operation)}
      end
    end
  end

  # Operation with custom telemetry identifier
  defmodule CustomTelemetryOperation do
    use Drops.Operations.Command, telemetry: [identifier: :my_app]

    steps do
      @impl true
      def execute(%{params: params}) do
        Process.sleep(15) # Simulate some work
        {:ok, Map.put(params, :processed_by, :custom_operation)}
      end
    end
  end

  # Operation with custom identifier and specific steps
  defmodule CustomStepTelemetryOperation do
    use Drops.Operations.Command, telemetry: [identifier: :my_app, steps: [:validate, :execute]]

    steps do
      def prepare(context), do: {:ok, context}
      
      def validate(%{params: params}) do
        Process.sleep(5) # Simulate validation
        {:ok, %{params: params, validated: true}}
      end

      @impl true
      def execute(%{params: params}) do
        Process.sleep(20) # Simulate some work
        {:ok, Map.put(params, :processed_by, :custom_step_operation)}
      end
    end
  end

  def run do
    Logger.configure(level: :info)
    
    # Set up telemetry handlers for both default and custom identifiers
    :telemetry.attach_many(
      "example-telemetry",
      [
        # Default identifier events
        [:drops, :operation, :start],
        [:drops, :operation, :stop],
        [:drops, :operation, :exception],
        [:drops, :operation, :step, :start],
        [:drops, :operation, :step, :stop],
        [:drops, :operation, :step, :exception],
        
        # Custom identifier events
        [:my_app, :operation, :start],
        [:my_app, :operation, :stop],
        [:my_app, :operation, :exception],
        [:my_app, :operation, :step, :start],
        [:my_app, :operation, :step, :stop],
        [:my_app, :operation, :step, :exception]
      ],
      &TelemetryHandler.handle_event/4,
      %{}
    )

    IO.puts("\n=== Telemetry Custom Identifier Example ===\n")

    # Example 1: Default telemetry (uses :drops identifier)
    IO.puts("1. Running operation with default telemetry identifier (:drops):")
    context = %{params: %{name: "example", type: "default"}}
    {:ok, result1} = DefaultTelemetryOperation.call(context)
    IO.puts("   Result: #{inspect(result1)}\n")

    Process.sleep(100) # Allow telemetry events to be processed

    # Example 2: Custom telemetry identifier
    IO.puts("2. Running operation with custom telemetry identifier (:my_app):")
    context = %{params: %{name: "example", type: "custom"}}
    {:ok, result2} = CustomTelemetryOperation.call(context)
    IO.puts("   Result: #{inspect(result2)}\n")

    Process.sleep(100) # Allow telemetry events to be processed

    # Example 3: Custom identifier with specific steps
    IO.puts("3. Running operation with custom identifier and specific step instrumentation:")
    context = %{params: %{name: "example", type: "custom_steps"}}
    {:ok, result3} = CustomStepTelemetryOperation.call(context)
    IO.puts("   Result: #{inspect(result3)}\n")

    Process.sleep(100) # Allow telemetry events to be processed

    # Clean up
    :telemetry.detach("example-telemetry")

    IO.puts("=== Example completed ===")
    IO.puts("\nNotice how the telemetry events are prefixed with different identifiers:")
    IO.puts("- Default operations use [:drops, :operation, ...] events")
    IO.puts("- Custom operations use [:my_app, :operation, ...] events")
    IO.puts("\nThis allows you to separate telemetry events by application or service!")
  end
end

# Run the example
TelemetryCustomIdentifierExample.run()
