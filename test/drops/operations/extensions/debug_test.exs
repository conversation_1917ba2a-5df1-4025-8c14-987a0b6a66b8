defmodule Drops.Operations.Extensions.DebugTest do
  use Drops.OperationCase, async: true
  import ExUnit.CaptureLog

  defp collect_telemetry_events(acc) do
    receive do
      {:telemetry_event, event, measurements, metadata} ->
        collect_telemetry_events([{event, measurements, metadata} | acc])
    after
      100 -> Enum.reverse(acc)
    end
  end

  describe "when debug is enabled" do
    operation type: :command, debug: true do
      schema do
        %{
          required(:name) => string(),
          required(:age) => integer()
        }
      end

      steps do
        @impl true
        def execute(context) do
          {:ok, context}
        end
      end
    end

    test "logging operation start/stop events by default", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}

      # Capture telemetry events to debug what's happening
      test_pid = self()

      :telemetry.attach_many(
        "debug-test-telemetry",
        [
          [:drops, :operation, :start],
          [:drops, :operation, :stop],
          [:drops, :operation, :step, :start],
          [:drops, :operation, :step, :stop]
        ],
        fn event, measurements, metadata, _config ->
          send(test_pid, {:telemetry_event, event, measurements, metadata})
        end,
        %{}
      )

      log_output =
        capture_log(fn ->
          {:ok, _result} = operation.call(context)
        end)

      # Clean up telemetry handler
      :telemetry.detach("debug-test-telemetry")

      # Debug: print what events we received
      IO.puts("Log output: #{inspect(log_output)}")

      # Collect all telemetry events
      events = collect_telemetry_events([])
      IO.puts("Telemetry events: #{inspect(events)}")

      # Debug: check if telemetry extension is enabled
      enabled_extensions = operation.__enabled_extensions__()
      IO.puts("Enabled extensions: #{inspect(enabled_extensions)}")

      # Debug: check operation options
      opts = operation.__opts__()
      IO.puts("Operation opts: #{inspect(opts)}")

      # Should log operation start/stop events (first and last steps)
      assert log_output =~ "Starting step conform in #{operation}"
      assert log_output =~ "Completed step conform in"
      assert log_output =~ "Starting step execute in #{operation}"
      assert log_output =~ "Completed step execute in"
    end

    test "logging operation's step start/stop events by default", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}

      log_output =
        capture_log(fn ->
          {:ok, _result} = operation.call(context)
        end)

      # Should log all step start/stop events
      assert log_output =~ "Starting step conform in #{operation}"
      assert log_output =~ "Completed step conform in"
      assert log_output =~ "Starting step prepare in #{operation}"
      assert log_output =~ "Completed step prepare in"
      assert log_output =~ "Starting step validate in #{operation}"
      assert log_output =~ "Completed step validate in"
      assert log_output =~ "Starting step execute in #{operation}"
      assert log_output =~ "Completed step execute in"
    end
  end

  describe "when debug is disabled" do
    operation type: :command, debug: false do
      schema do
        %{
          required(:name) => string(),
          required(:age) => integer()
        }
      end

      steps do
        @impl true
        def execute(context) do
          {:ok, context}
        end
      end
    end

    test "does not log debug messages", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}

      log_output =
        capture_log(fn ->
          {:ok, _result} = operation.call(context)
        end)

      # Should not contain debug logs from the Debug extension
      refute log_output =~ "Starting step"
      refute log_output =~ "Completed step"
    end
  end

  describe "when debug is enabled with custom identifier" do
    operation type: :command, debug: [identifier: :my_app] do
      schema do
        %{
          required(:name) => string(),
          required(:age) => integer()
        }
      end

      steps do
        @impl true
        def execute(context) do
          {:ok, context}
        end
      end
    end

    test "logs debug messages with custom identifier", %{operation: operation} do
      context = %{params: %{name: "test", age: 30}}

      log_output =
        capture_log(fn ->
          {:ok, _result} = operation.call(context)
        end)

      # Should log all step start/stop events
      assert log_output =~ "Starting step conform in #{operation}"
      assert log_output =~ "Completed step conform in"
      assert log_output =~ "Starting step execute in #{operation}"
      assert log_output =~ "Completed step execute in"
    end
  end
end
